// API Service para MusicApp
// Este archivo maneja las peticiones a APIs externas

// Configuración de la API
const API_CONFIG = {
    // Configuración para la API de Uberchord (principal)
    uberchord: {
        baseUrl: 'https://api.uberchord.com/v1',
        timeout: 8000,
        enabled: true
    },
    // Configuración para la API de Hooktheory (alternativa)
    hooktheory: {
        baseUrl: 'https://api.hooktheory.com/v1',
        // Nota: En una aplicación real, nunca expongas tus credenciales en el código frontend
        // Esto es solo para fines de demostración
        username: 'tu-usuario',
        password: 'tu-contraseña',
        authToken: null, // Se llenará después de la autenticación
        timeout: 10000,
        enabled: false // Deshabilitado por defecto
    },
    // Configuración para usar nuestra API simulada como fallback
    useSimulation: true, // Cambiar a false para usar solo APIs reales
    timeout: 10000,
    // Sistema de caché para optimizar llamadas
    cache: {
        enabled: true,
        duration: 300000, // 5 minutos en milisegundos
        storage: new Map()
    }
};

// Clase para manejar errores de la API
class ApiError extends Error {
    constructor(message, status, data) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.data = data;
    }
}

// Sistema de caché para optimizar llamadas a la API
const CacheManager = {
    // Generar clave de caché
    generateKey: function(endpoint, params = {}) {
        const paramString = Object.keys(params)
            .sort()
            .map(key => `${key}=${params[key]}`)
            .join('&');
        return `${endpoint}?${paramString}`;
    },

    // Obtener datos del caché
    get: function(key) {
        if (!API_CONFIG.cache.enabled) return null;

        const cached = API_CONFIG.cache.storage.get(key);
        if (!cached) return null;

        // Verificar si el caché ha expirado
        if (Date.now() - cached.timestamp > API_CONFIG.cache.duration) {
            API_CONFIG.cache.storage.delete(key);
            return null;
        }

        return cached.data;
    },

    // Guardar datos en el caché
    set: function(key, data) {
        if (!API_CONFIG.cache.enabled) return;

        API_CONFIG.cache.storage.set(key, {
            data: data,
            timestamp: Date.now()
        });
    },

    // Limpiar caché expirado
    cleanup: function() {
        if (!API_CONFIG.cache.enabled) return;

        const now = Date.now();
        for (const [key, value] of API_CONFIG.cache.storage.entries()) {
            if (now - value.timestamp > API_CONFIG.cache.duration) {
                API_CONFIG.cache.storage.delete(key);
            }
        }
    }
};

// Función para autenticar con la API de Hooktheory
async function authenticateWithHooktheory() {
    if (API_CONFIG.hooktheory.authToken) {
        return API_CONFIG.hooktheory.authToken;
    }

    try {
        const response = await fetch(`${API_CONFIG.hooktheory.baseUrl}/users/auth`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: API_CONFIG.hooktheory.username,
                password: API_CONFIG.hooktheory.password
            })
        });

        const data = await response.json();

        if (!response.ok) {
            console.error('Error de autenticación con Hooktheory:', data);
            return null;
        }

        // Guardar el token para futuras peticiones
        API_CONFIG.hooktheory.authToken = data.activkey;
        return data.activkey;
    } catch (error) {
        console.error('Error al autenticar con Hooktheory:', error);
        return null;
    }
}

// Función para realizar peticiones a la API
async function fetchFromApi(endpoint, options = {}, apiProvider = 'hooktheory') {
    // Determinar la configuración de la API a utilizar
    const apiConfig = API_CONFIG[apiProvider] || API_CONFIG.hooktheory;
    const url = `${apiConfig.baseUrl}${endpoint}`;

    let defaultOptions = {
        headers: {
            'Content-Type': 'application/json'
        },
        timeout: apiConfig.timeout || API_CONFIG.timeout
    };

    // Añadir autenticación según el proveedor de API
    if (apiProvider === 'hooktheory') {
        const authToken = await authenticateWithHooktheory();
        if (authToken) {
            defaultOptions.headers['Authorization'] = `Bearer ${authToken}`;
        }
    } else if (apiProvider === 'chordify' && apiConfig.apiKey) {
        defaultOptions.headers['X-API-Key'] = apiConfig.apiKey;
    }

    const fetchOptions = { ...defaultOptions, ...options };

    try {
        // Crear un controlador de tiempo de espera
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), fetchOptions.timeout);

        fetchOptions.signal = controller.signal;

        const response = await fetch(url, fetchOptions);
        clearTimeout(timeoutId);

        const data = await response.json();

        if (!response.ok) {
            throw new ApiError(
                data.message || 'Error en la petición a la API',
                response.status,
                data
            );
        }

        return {
            success: true,
            data: data
        };
    } catch (error) {
        if (error.name === 'AbortError') {
            console.error('La petición ha excedido el tiempo de espera');
            return { success: false, error: 'La petición ha excedido el tiempo de espera' };
        }

        console.error('Error en la petición a la API:', error);

        // Devolver un objeto de error en lugar de lanzar una excepción
        return {
            success: false,
            error: error.message || 'Error desconocido en la petición a la API'
        };
    }
}

// Simulación de API para acordes (mientras no tengamos una API real)
const chordApiSimulator = {
    // Base de datos simulada de acordes
    chordDatabase: {
        guitar: {
            // Acordes mayores
            'C': {
                name: 'C Mayor',
                notes: ['C', 'E', 'G'],
                intervals: ['1', '3', '5'],
                positions: [
                    {
                        name: 'Posición abierta',
                        difficulty: 'Principiante',
                        fingers: [
                            { string: 5, fret: 3, finger: 3 },
                            { string: 4, fret: 2, finger: 2 },
                            { string: 2, fret: 1, finger: 1 }
                        ],
                        muted: [6],
                        barres: []
                    },
                    {
                        name: 'Posición de barra',
                        difficulty: 'Intermedio',
                        fingers: [
                            { string: 6, fret: 8, finger: 1 },
                            { string: 5, fret: 10, finger: 3 },
                            { string: 4, fret: 10, finger: 4 },
                            { string: 3, fret: 9, finger: 2 },
                            { string: 2, fret: 8, finger: 1 },
                            { string: 1, fret: 8, finger: 1 }
                        ],
                        muted: [],
                        barres: [{ fromString: 6, toString: 1, fret: 8 }]
                    }
                ],
                related: ['Am', 'F', 'G', 'Em', 'Dm'],
                progressions: ['C-G-Am-F', 'C-Am-F-G', 'C-F-G-C'],
                description: 'El acorde de Do mayor es uno de los acordes más básicos y utilizados en música. Está formado por las notas Do, Mi y Sol.'
            },
            'G': {
                name: 'G Mayor',
                notes: ['G', 'B', 'D'],
                intervals: ['1', '3', '5'],
                positions: [
                    {
                        name: 'Posición abierta',
                        difficulty: 'Principiante',
                        fingers: [
                            { string: 6, fret: 3, finger: 3 },
                            { string: 5, fret: 2, finger: 2 },
                            { string: 1, fret: 3, finger: 4 }
                        ],
                        muted: [],
                        barres: []
                    },
                    {
                        name: 'Posición de barra',
                        difficulty: 'Intermedio',
                        fingers: [
                            { string: 6, fret: 3, finger: 1 },
                            { string: 5, fret: 5, finger: 3 },
                            { string: 4, fret: 5, finger: 4 },
                            { string: 3, fret: 4, finger: 2 },
                            { string: 2, fret: 3, finger: 1 },
                            { string: 1, fret: 3, finger: 1 }
                        ],
                        muted: [],
                        barres: [{ fromString: 6, toString: 1, fret: 3 }]
                    }
                ],
                related: ['Em', 'C', 'D', 'Am', 'Bm'],
                progressions: ['G-D-Em-C', 'G-C-D-G', 'G-Em-C-D'],
                description: 'El acorde de Sol mayor es muy común en música folk y country. Está formado por las notas Sol, Si y Re.'
            },
            'D': {
                name: 'D Mayor',
                notes: ['D', 'F#', 'A'],
                intervals: ['1', '3', '5'],
                positions: [
                    {
                        name: 'Posición abierta',
                        difficulty: 'Principiante',
                        fingers: [
                            { string: 4, fret: 0, finger: 0 },
                            { string: 3, fret: 2, finger: 2 },
                            { string: 2, fret: 3, finger: 3 },
                            { string: 1, fret: 2, finger: 1 }
                        ],
                        muted: [6, 5],
                        barres: []
                    }
                ],
                related: ['Bm', 'G', 'A', 'F#m', 'Em'],
                progressions: ['D-A-Bm-G', 'D-G-A-D', 'D-Bm-G-A'],
                description: 'El acorde de Re mayor es brillante y optimista. Está formado por las notas Re, Fa# y La.'
            }
        },
        piano: {
            'C': {
                name: 'C Mayor',
                notes: ['C', 'E', 'G'],
                intervals: ['1', '3', '5'],
                keys: ['C', 'E', 'G'],
                fingering: 'Mano derecha: 1-3-5, Mano izquierda: 5-3-1',
                inversions: [
                    { name: 'Fundamental', notes: ['C', 'E', 'G'] },
                    { name: 'Primera inversión', notes: ['E', 'G', 'C'] },
                    { name: 'Segunda inversión', notes: ['G', 'C', 'E'] }
                ],
                related: ['Am', 'F', 'G', 'Em', 'Dm'],
                progressions: ['C-G-Am-F', 'C-Am-F-G', 'C-F-G-C'],
                description: 'El acorde de Do mayor en piano es uno de los primeros que aprenden los principiantes. Está formado por las teclas Do, Mi y Sol.'
            }
        },
        bass: {
            'C': {
                name: 'C Mayor',
                notes: ['C', 'E', 'G'],
                intervals: ['1', '3', '5'],
                positions: [
                    {
                        name: 'Posición raíz en 3ª cuerda',
                        fingers: [
                            { string: 3, fret: 3, finger: 1 }
                        ]
                    },
                    {
                        name: 'Tríada completa',
                        fingers: [
                            { string: 3, fret: 3, finger: 1 },
                            { string: 2, fret: 5, finger: 3 },
                            { string: 1, fret: 3, finger: 1 }
                        ]
                    }
                ],
                related: ['Am', 'F', 'G'],
                progressions: ['C-G-Am-F', 'C-F-G-C'],
                description: 'En el bajo, el acorde de Do mayor se toca generalmente como una nota individual (la raíz) o como un arpegio de las notas Do, Mi y Sol.'
            }
        }
    },

    // Método para obtener información de un acorde
    getChord: function(instrument, root, type = 'major') {
        // Convertir el tipo a un formato estándar
        const normalizedType = this.normalizeChordType(type);

        // Verificar si tenemos el acorde en nuestra base de datos
        if (this.chordDatabase[instrument] &&
            this.chordDatabase[instrument][root]) {
            return {
                success: true,
                data: this.chordDatabase[instrument][root]
            };
        }

        // Si no tenemos el acorde, devolver un error
        return {
            success: false,
            error: `No se encontró información para el acorde ${root} ${type} en ${instrument}`
        };
    },

    // Método para normalizar el tipo de acorde
    normalizeChordType: function(type) {
        const typeMap = {
            'major': 'major',
            'maj': 'major',
            'M': 'major',
            'minor': 'minor',
            'min': 'minor',
            'm': 'minor',
            'diminished': 'diminished',
            'dim': 'diminished',
            'augmented': 'augmented',
            'aug': 'augmented',
            '7': 'dominant7',
            'dominant7': 'dominant7',
            'maj7': 'major7',
            'major7': 'major7',
            'min7': 'minor7',
            'minor7': 'minor7',
            'm7': 'minor7'
        };

        return typeMap[type.toLowerCase()] || type;
    },

    // Método para buscar acordes
    searchChords: function(query) {
        const results = [];

        // Buscar en todos los instrumentos
        for (const instrument in this.chordDatabase) {
            for (const root in this.chordDatabase[instrument]) {
                const chord = this.chordDatabase[instrument][root];

                // Verificar si el nombre del acorde contiene la consulta
                if (chord.name.toLowerCase().includes(query.toLowerCase())) {
                    results.push({
                        instrument,
                        root,
                        name: chord.name,
                        notes: chord.notes
                    });
                }
            }
        }

        return {
            success: true,
            count: results.length,
            data: results
        };
    }
};

// Servicio mejorado de API para acordes
const chordApiService = {
    // Fuentes de imágenes de acordes con fallback
    imageSources: [
        {
            name: 'uberchord',
            getUrl: function(instrument, root, type) {
                // Usar la API de Uberchord para obtener imágenes
                const normalizedRoot = root.replace('#', '%23').replace('b', 'b');
                const normalizedType = type === 'major' ? '' : `_${type}`;

                if (instrument === 'guitar') {
                    return `https://api.uberchord.com/v1/embed/chords?names=${normalizedRoot}${normalizedType}`;
                }
                return null; // Uberchord solo soporta guitarra
            }
        },
        {
            name: 'local',
            getUrl: function(instrument, root, type) {
                // Usar imágenes locales como fallback
                const normalizedType = type === 'major' ? 'major' : type;
                return `../images/chords/${instrument}/${root}-${normalizedType}.png`;
            }
        },
        {
            name: 'scales-chords',
            getUrl: function(instrument, root, type) {
                // Usar scales-chords.com como último recurso
                const normalizedType = type === 'major' ? 'major' : type;
                const normalizedRoot = root.replace('#', 'sharp').replace('b', 'flat');
                return `https://www.scales-chords.com/chord-charts/${instrument}-${normalizedRoot}-${normalizedType}.png`;
            }
        }
    ],

    // Obtener imagen de un acorde con sistema de fallback
    async getChordImage(instrument, root, type = 'major') {
        // Verificar caché primero
        const cacheKey = CacheManager.generateKey('chord-image', { instrument, root, type });
        const cachedResult = CacheManager.get(cacheKey);
        if (cachedResult) {
            return cachedResult;
        }

        // Intentar obtener la imagen desde múltiples fuentes
        for (const source of this.imageSources) {
            try {
                const imageUrl = source.getUrl(instrument, root, type);
                if (!imageUrl) continue;

                // Verificar si la imagen existe y es accesible
                const isValid = await this.validateImageUrl(imageUrl);
                if (isValid) {
                    const result = {
                        success: true,
                        data: {
                            imageUrl: imageUrl,
                            altText: `${root} ${type} en ${instrument}`,
                            source: source.name
                        }
                    };

                    // Guardar en caché
                    CacheManager.set(cacheKey, result);
                    return result;
                }
            } catch (error) {
                console.warn(`Error con fuente ${source.name}:`, error);
                continue;
            }
        }

        // Si ninguna fuente funciona, devolver error
        const errorResult = {
            success: false,
            error: 'No se pudo obtener la imagen del acorde desde ninguna fuente'
        };

        // Guardar error en caché por un tiempo corto
        CacheManager.set(cacheKey, errorResult);
        return errorResult;
    },

    // Validar si una URL de imagen es accesible
    async validateImageUrl(url) {
        try {
            const response = await fetch(url, {
                method: 'HEAD',
                timeout: 3000,
                mode: 'no-cors' // Para evitar problemas de CORS
            });
            return true; // Si no hay error, asumimos que la imagen existe
        } catch (error) {
            // Para URLs locales, intentar cargar la imagen
            if (url.startsWith('../')) {
                return new Promise((resolve) => {
                    const img = new Image();
                    img.onload = () => resolve(true);
                    img.onerror = () => resolve(false);
                    img.src = url;
                });
            }
            return false;
        }
    },

    // Obtener información de acordes desde Uberchord API
    async getChordFromUberchord(root, type = 'major') {
        try {
            const cacheKey = CacheManager.generateKey('uberchord-chord', { root, type });
            const cachedResult = CacheManager.get(cacheKey);
            if (cachedResult) {
                return cachedResult;
            }

            // Normalizar el nombre del acorde para Uberchord
            const normalizedRoot = root.replace('#', '%23').replace('b', 'b');
            const normalizedType = type === 'major' ? '' : `_${type}`;
            const chordName = `${normalizedRoot}${normalizedType}`;

            const response = await fetch(`${API_CONFIG.uberchord.baseUrl}/chords/${chordName}`, {
                method: 'GET',
                timeout: API_CONFIG.uberchord.timeout
            });

            if (response.ok) {
                const data = await response.json();
                const result = {
                    success: true,
                    data: this.transformUberchordResponse(data, root, type)
                };

                CacheManager.set(cacheKey, result);
                return result;
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error al obtener acorde de Uberchord:', error);
            return {
                success: false,
                error: error.message
            };
        }
    },

    // Transformar respuesta de Uberchord al formato interno
    transformUberchordResponse(data, root, type) {
        return {
            name: `${root} ${type}`,
            strings: data.strings,
            fingering: data.fingering,
            chordName: data.chordName,
            source: 'uberchord'
        };
    },

    // Normalizar el tipo de acorde para la URL
    normalizeChordTypeForUrl(type) {
        const typeMap = {
            'major': 'major',
            'minor': 'minor',
            'm': 'minor',
            'dim': 'dim',
            'diminished': 'dim',
            'aug': 'aug',
            'augmented': 'aug',
            '7': '7',
            'dominant7': '7',
            'maj7': 'maj7',
            'major7': 'maj7',
            'min7': 'min7',
            'minor7': 'min7',
            'm7': 'min7',
            'sus2': 'sus2',
            'sus4': 'sus4',
            '9': '9',
            'add9': 'add9'
        };

        return typeMap[type.toLowerCase()] || type;
    },

    // Obtener información de un acorde desde la API real o simulada
    async getChordInfo(instrument, root, type = 'major') {
        // Si estamos usando la simulación, devolver datos simulados
        if (API_CONFIG.useSimulation) {
            return chordApiSimulator.getChord(instrument, root, type);
        }

        // Intentar obtener datos de la API real
        try {
            // Normalizar el tipo de acorde para la API
            const normalizedType = chordApiSimulator.normalizeChordType(type);

            // Mapear el instrumento a un formato que entienda la API
            const apiInstrument = instrument === 'guitar' ? 'guitar' :
                                 instrument === 'piano' ? 'piano' : 'bass';

            // Construir el endpoint según la API que estemos usando
            let endpoint = '';
            let apiProvider = 'hooktheory';

            if (apiProvider === 'hooktheory') {
                // Hooktheory usa un formato diferente para los acordes
                const hooktheoryRoot = this.mapRootToHooktheory(root);
                const hooktheoryType = this.mapTypeToHooktheory(type);
                endpoint = `/chords/${hooktheoryRoot}${hooktheoryType}`;
            } else if (apiProvider === 'chordify') {
                endpoint = `/chords/${root}${type}?instrument=${apiInstrument}`;
            }

            // Realizar la petición a la API
            const response = await fetchFromApi(endpoint, { method: 'GET' }, apiProvider);

            // Si la petición fue exitosa, transformar los datos al formato que espera nuestra aplicación
            if (response.success) {
                return {
                    success: true,
                    data: this.transformApiResponse(response.data, instrument, root, type, apiProvider)
                };
            }

            // Si hubo un error, intentar con la simulación como fallback
            console.log('Error al obtener datos de la API, usando simulación como fallback');
            return chordApiSimulator.getChord(instrument, root, type);
        } catch (error) {
            console.error('Error al obtener información del acorde:', error);
            // Usar simulación como fallback
            return chordApiSimulator.getChord(instrument, root, type);
        }
    },

    // Buscar acordes en la API
    async searchChords(query, instrument = 'all') {
        // Si estamos usando la simulación, devolver datos simulados
        if (API_CONFIG.useSimulation) {
            return chordApiSimulator.searchChords(query);
        }

        // Intentar buscar en la API real
        try {
            let endpoint = '';
            let apiProvider = 'hooktheory';

            if (apiProvider === 'hooktheory') {
                endpoint = `/search/chords?q=${encodeURIComponent(query)}`;
            } else if (apiProvider === 'chordify') {
                endpoint = `/search/chords?query=${encodeURIComponent(query)}&instrument=${instrument}`;
            }

            const response = await fetchFromApi(endpoint, { method: 'GET' }, apiProvider);

            if (response.success) {
                return {
                    success: true,
                    data: this.transformSearchResults(response.data, apiProvider)
                };
            }

            // Si hubo un error, intentar con la simulación como fallback
            return chordApiSimulator.searchChords(query);
        } catch (error) {
            console.error('Error al buscar acordes:', error);
            // Usar simulación como fallback
            return chordApiSimulator.searchChords(query);
        }
    },

    // Mapear la nota raíz al formato de Hooktheory
    mapRootToHooktheory(root) {
        const mapping = {
            'C': '1',
            'C#': '2', 'Db': '2',
            'D': '3',
            'D#': '4', 'Eb': '4',
            'E': '5',
            'F': '6',
            'F#': '7', 'Gb': '7',
            'G': '8',
            'G#': '9', 'Ab': '9',
            'A': '10',
            'A#': '11', 'Bb': '11',
            'B': '12'
        };

        return mapping[root] || '1';
    },

    // Mapear el tipo de acorde al formato de Hooktheory
    mapTypeToHooktheory(type) {
        const mapping = {
            'major': 'M',
            'minor': 'm',
            'diminished': 'd',
            'augmented': 'A',
            'dominant7': '7',
            'major7': 'M7',
            'minor7': 'm7'
        };

        return mapping[type] || '';
    },

    // Transformar la respuesta de la API al formato que espera nuestra aplicación
    transformApiResponse(apiData, instrument, root, type, apiProvider) {
        // Implementación específica según la API que estemos usando
        if (apiProvider === 'hooktheory') {
            // Transformar datos de Hooktheory
            return {
                name: `${root} ${type}`,
                notes: apiData.notes || [],
                intervals: apiData.intervals || [],
                positions: apiData.positions || [],
                related: apiData.related || [],
                progressions: apiData.progressions || [],
                description: apiData.description || `Acorde de ${root} ${type}`
            };
        } else if (apiProvider === 'chordify') {
            // Transformar datos de Chordify
            return {
                name: apiData.name || `${root} ${type}`,
                notes: apiData.notes || [],
                intervals: apiData.intervals || [],
                positions: apiData.positions || [],
                related: apiData.related || [],
                progressions: apiData.progressions || [],
                description: apiData.description || `Acorde de ${root} ${type}`
            };
        }

        // Si no reconocemos el proveedor, devolver un objeto vacío
        return {};
    },

    // Transformar resultados de búsqueda
    transformSearchResults(apiData, apiProvider) {
        if (apiProvider === 'hooktheory') {
            // Transformar resultados de Hooktheory
            return apiData.map(item => ({
                instrument: item.instrument || 'guitar',
                root: item.root || 'C',
                name: item.name || 'C Major',
                notes: item.notes || []
            }));
        } else if (apiProvider === 'chordify') {
            // Transformar resultados de Chordify
            return apiData.map(item => ({
                instrument: item.instrument || 'guitar',
                root: item.root || 'C',
                name: item.name || 'C Major',
                notes: item.notes || []
            }));
        }

        return [];
    }
};

// Inicialización del sistema de caché
function initializeApiService() {
    // Limpiar caché expirado cada 5 minutos
    setInterval(() => {
        CacheManager.cleanup();
    }, 300000); // 5 minutos

    console.log('API Service inicializado con sistema de caché');
}

// Exportar las funciones y objetos para uso en otros archivos
window.MusicAppApi = {
    fetchFromApi,
    chordApiSimulator,
    chordApiService,
    CacheManager,
    initializeApiService
};

// Inicializar automáticamente cuando se carga el script
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApiService);
} else {
    initializeApiService();
}
